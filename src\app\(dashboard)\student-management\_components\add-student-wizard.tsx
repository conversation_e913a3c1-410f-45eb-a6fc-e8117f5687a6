// src/app/student-management/_components/add-student-wizard.tsx
'use client';

import { useState } from 'react';
import './student-wizard.css';

interface AddStudentWizardProps {
  onClose: () => void;
  onSuccess?: (message: string) => void;
  onError?: (error: string) => void;
}

const AddStudentWizard = ({ onClose, onSuccess, onError }: AddStudentWizardProps) => {
  const [currentStep, setCurrentStep] = useState(1);
  const [isSubmitting, setIsSubmitting] = useState(false);

  const [formData, setFormData] = useState({
    // Personal Info
    firstName: '',
    lastName: '',
    dateOfBirth: '',
    gender: '',
    email: '',
    phoneNumber: '',
    address: '',
    
    // Guardian Details
    guardianType: '',
    guardianName: '',
    guardianPhone: '',
    guardianEmail: '',
    guardianAddress: '',
    
    // Academic Info
    rollNumber: '',
    className: '',
    section: '',
    academicYear: '2024-25'
  });

  const steps = [
    { number: 1, title: 'Personal', description: 'Basic Information' },
    { number: 2, title: 'Guardian', description: 'Guardian Details' },
    { number: 3, title: 'Academic', description: 'Academic Information' },
    { number: 4, title: 'Review', description: 'Review & Submit' }
  ];

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleNext = () => {
    if (currentStep < steps.length) {
      setCurrentStep(currentStep + 1);
    }
  };

  const handlePrevious = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    }
  };

  const handleSubmit = async () => {
    setIsSubmitting(true);
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 2000));
      console.log('Student data submitted:', formData);
      onSuccess?.('Student added successfully!');
      onClose();
    } catch (error) {
      onError?.('Failed to add student. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  const renderPersonalInfoStep = () => (
    <div className="space-y-5">
      <div className="grid grid-cols-2 gap-4">
        <div>
          <label className="block text-sm font-semibold text-slate-700 mb-2">First Name *</label>
          <input
            type="text"
            value={formData.firstName}
            onChange={(e) => handleInputChange('firstName', e.target.value)}
            className="w-full px-4 py-3 border-2 border-slate-200 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500 bg-white shadow-sm hover:border-slate-300 transition-all duration-200"
            placeholder="Enter first name"
          />
        </div>
        <div>
          <label className="block text-sm font-semibold text-slate-700 mb-2">Last Name *</label>
          <input
            type="text"
            value={formData.lastName}
            onChange={(e) => handleInputChange('lastName', e.target.value)}
            className="w-full px-4 py-3 border-2 border-slate-200 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500 bg-white shadow-sm hover:border-slate-300 transition-all duration-200"
            placeholder="Enter last name"
          />
        </div>
      </div>
      
      <div className="grid grid-cols-2 gap-4">
        <div>
          <label className="block text-sm font-semibold text-slate-700 mb-2">Date of Birth *</label>
          <input
            type="date"
            value={formData.dateOfBirth}
            onChange={(e) => handleInputChange('dateOfBirth', e.target.value)}
            className="w-full px-4 py-3 border-2 border-slate-200 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500 bg-white shadow-sm hover:border-slate-300 transition-all duration-200"
          />
        </div>
        <div>
          <label className="block text-sm font-semibold text-slate-700 mb-2">Gender *</label>
          <select
            value={formData.gender}
            onChange={(e) => handleInputChange('gender', e.target.value)}
            className="w-full px-4 py-3 border-2 border-slate-200 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500 bg-white shadow-sm hover:border-slate-300 transition-all duration-200"
          >
            <option value="">Select Gender</option>
            <option value="male">Male</option>
            <option value="female">Female</option>
            <option value="other">Other</option>
          </select>
        </div>
      </div>

      <div>
        <label className="block text-sm font-semibold text-slate-700 mb-2">Email</label>
        <input
          type="email"
          value={formData.email}
          onChange={(e) => handleInputChange('email', e.target.value)}
          className="w-full px-4 py-3 border-2 border-slate-200 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500 bg-white shadow-sm hover:border-slate-300 transition-all duration-200"
          placeholder="Enter email address"
        />
      </div>

      <div>
        <label className="block text-sm font-semibold text-slate-700 mb-2">Phone Number</label>
        <input
          type="tel"
          value={formData.phoneNumber}
          onChange={(e) => handleInputChange('phoneNumber', e.target.value)}
          className="w-full px-4 py-3 border-2 border-slate-200 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500 bg-white shadow-sm hover:border-slate-300 transition-all duration-200"
          placeholder="Enter phone number"
        />
      </div>

      <div>
        <label className="block text-sm font-semibold text-slate-700 mb-2">Address</label>
        <textarea
          value={formData.address}
          onChange={(e) => handleInputChange('address', e.target.value)}
          className="w-full px-4 py-3 border-2 border-slate-200 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500 bg-white shadow-sm hover:border-slate-300 transition-all duration-200 resize-none"
          rows={3}
          placeholder="Enter address"
        />
      </div>
    </div>
  );

  const renderGuardianStep = () => (
    <div className="space-y-5">
      <div>
        <label className="block text-sm font-semibold text-slate-700 mb-2">Guardian Type *</label>
        <select
          value={formData.guardianType}
          onChange={(e) => handleInputChange('guardianType', e.target.value)}
          className="w-full px-4 py-3 border-2 border-slate-200 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500 bg-white shadow-sm hover:border-slate-300 transition-all duration-200"
        >
          <option value="">Select Guardian Type</option>
          <option value="father">Father</option>
          <option value="mother">Mother</option>
          <option value="grandfather">Grandfather</option>
          <option value="grandmother">Grandmother</option>
          <option value="uncle">Uncle</option>
          <option value="aunt">Aunt</option>
          <option value="elder_brother">Elder Brother</option>
          <option value="elder_sister">Elder Sister</option>
          <option value="legal_guardian">Legal Guardian</option>
          <option value="other">Other</option>
        </select>
      </div>

      <div>
        <label className="block text-sm font-semibold text-slate-700 mb-2">Guardian Name *</label>
        <input
          type="text"
          value={formData.guardianName}
          onChange={(e) => handleInputChange('guardianName', e.target.value)}
          className="w-full px-4 py-3 border-2 border-slate-200 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500 bg-white shadow-sm hover:border-slate-300 transition-all duration-200"
          placeholder="Enter guardian name"
        />
      </div>

      <div>
        <label className="block text-sm font-semibold text-slate-700 mb-2">Guardian Phone *</label>
        <input
          type="tel"
          value={formData.guardianPhone}
          onChange={(e) => handleInputChange('guardianPhone', e.target.value)}
          className="w-full px-4 py-3 border-2 border-slate-200 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500 bg-white shadow-sm hover:border-slate-300 transition-all duration-200"
          placeholder="Enter guardian phone"
        />
      </div>

      <div>
        <label className="block text-sm font-semibold text-slate-700 mb-2">Guardian Email</label>
        <input
          type="email"
          value={formData.guardianEmail}
          onChange={(e) => handleInputChange('guardianEmail', e.target.value)}
          className="w-full px-4 py-3 border-2 border-slate-200 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500 bg-white shadow-sm hover:border-slate-300 transition-all duration-200"
          placeholder="Enter guardian email"
        />
      </div>

      <div>
        <label className="block text-sm font-semibold text-slate-700 mb-2">Guardian Address</label>
        <textarea
          value={formData.guardianAddress}
          onChange={(e) => handleInputChange('guardianAddress', e.target.value)}
          className="w-full px-4 py-3 border-2 border-slate-200 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500 bg-white shadow-sm hover:border-slate-300 transition-all duration-200 resize-none"
          rows={3}
          placeholder="Enter guardian address"
        />
      </div>
    </div>
  );

  const renderAcademicStep = () => (
    <div className="space-y-5">
      <div>
        <label className="block text-sm font-semibold text-slate-700 mb-2">Roll Number *</label>
        <input
          type="text"
          value={formData.rollNumber}
          onChange={(e) => handleInputChange('rollNumber', e.target.value)}
          className="w-full px-4 py-3 border-2 border-slate-200 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500 bg-white shadow-sm hover:border-slate-300 transition-all duration-200"
          placeholder="Enter roll number"
        />
      </div>

      <div className="grid grid-cols-2 gap-4">
        <div>
          <label className="block text-sm font-semibold text-slate-700 mb-2">Class *</label>
          <select
            value={formData.className}
            onChange={(e) => handleInputChange('className', e.target.value)}
            className="w-full px-4 py-3 border-2 border-slate-200 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500 bg-white shadow-sm hover:border-slate-300 transition-all duration-200"
          >
            <option value="">Select Class</option>
            <option value="1">Class 1</option>
            <option value="2">Class 2</option>
            <option value="3">Class 3</option>
            <option value="4">Class 4</option>
            <option value="5">Class 5</option>
            <option value="6">Class 6</option>
            <option value="7">Class 7</option>
            <option value="8">Class 8</option>
            <option value="9">Class 9</option>
            <option value="10">Class 10</option>
            <option value="11">Class 11</option>
            <option value="12">Class 12</option>
          </select>
        </div>
        <div>
          <label className="block text-sm font-semibold text-slate-700 mb-2">Section *</label>
          <select
            value={formData.section}
            onChange={(e) => handleInputChange('section', e.target.value)}
            className="w-full px-4 py-3 border-2 border-slate-200 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500 bg-white shadow-sm hover:border-slate-300 transition-all duration-200"
          >
            <option value="">Select Section</option>
            <option value="A">Section A</option>
            <option value="B">Section B</option>
            <option value="C">Section C</option>
            <option value="D">Section D</option>
          </select>
        </div>
      </div>

      <div>
        <label className="block text-sm font-semibold text-slate-700 mb-2">Academic Year *</label>
        <input
          type="text"
          value={formData.academicYear}
          onChange={(e) => handleInputChange('academicYear', e.target.value)}
          className="w-full px-4 py-3 border-2 border-slate-200 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500 bg-white shadow-sm hover:border-slate-300 transition-all duration-200"
          placeholder="Enter academic year (e.g., 2024-25)"
        />
      </div>
    </div>
  );

  const renderReviewStep = () => (
    <div className="space-y-5">
      <div className="bg-gradient-to-br from-emerald-50 to-cyan-50 p-5 rounded-xl border border-emerald-200/50 shadow-sm">
        <div className="flex items-center space-x-3 mb-4">
          <div className="w-8 h-8 bg-gradient-to-r from-emerald-500 to-cyan-600 rounded-lg flex items-center justify-center">
            <svg className="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
            </svg>
          </div>
          <h3 className="font-semibold text-slate-800">Personal Information</h3>
        </div>
        <div className="grid grid-cols-2 gap-3 text-sm">
          <div className="flex justify-between">
            <span className="font-medium text-slate-600">Name:</span> 
            <span className="text-slate-800">{formData.firstName} {formData.lastName}</span>
          </div>
          <div className="flex justify-between">
            <span className="font-medium text-slate-600">DOB:</span> 
            <span className="text-slate-800">{formData.dateOfBirth}</span>
          </div>
          <div className="flex justify-between">
            <span className="font-medium text-slate-600">Gender:</span> 
            <span className="text-slate-800 capitalize">{formData.gender}</span>
          </div>
          <div className="flex justify-between">
            <span className="font-medium text-slate-600">Email:</span> 
            <span className="text-slate-800">{formData.email || 'Not provided'}</span>
          </div>
        </div>
      </div>

      <div className="bg-gradient-to-br from-cyan-50 to-sky-50 p-5 rounded-xl border border-cyan-200/50 shadow-sm">
        <div className="flex items-center space-x-3 mb-4">
          <div className="w-8 h-8 bg-gradient-to-r from-cyan-500 to-sky-600 rounded-lg flex items-center justify-center">
            <svg className="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />
            </svg>
          </div>
          <h3 className="font-semibold text-slate-800">Guardian Information</h3>
        </div>
        <div className="grid grid-cols-2 gap-3 text-sm">
          <div className="flex justify-between">
            <span className="font-medium text-slate-600">Name:</span> 
            <span className="text-slate-800">{formData.guardianName}</span>
          </div>
          <div className="flex justify-between">
            <span className="font-medium text-slate-600">Phone:</span> 
            <span className="text-slate-800">{formData.guardianPhone}</span>
          </div>
          <div className="flex justify-between col-span-2">
            <span className="font-medium text-slate-600">Email:</span> 
            <span className="text-slate-800">{formData.guardianEmail || 'Not provided'}</span>
          </div>
        </div>
      </div>

      <div className="bg-gradient-to-br from-sky-50 to-indigo-50 p-5 rounded-xl border border-sky-200/50 shadow-sm">
        <div className="flex items-center space-x-3 mb-4">
          <div className="w-8 h-8 bg-gradient-to-r from-sky-500 to-indigo-600 rounded-lg flex items-center justify-center">
            <svg className="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
            </svg>
          </div>
          <h3 className="font-semibold text-slate-800">Academic Information</h3>
        </div>
        <div className="grid grid-cols-2 gap-3 text-sm">
          <div className="flex justify-between">
            <span className="font-medium text-slate-600">Roll Number:</span> 
            <span className="text-slate-800">{formData.rollNumber}</span>
          </div>
          <div className="flex justify-between">
            <span className="font-medium text-slate-600">Class:</span> 
            <span className="text-slate-800">Class {formData.className}</span>
          </div>
          <div className="flex justify-between">
            <span className="font-medium text-slate-600">Section:</span> 
            <span className="text-slate-800">Section {formData.section}</span>
          </div>
          <div className="flex justify-between">
            <span className="font-medium text-slate-600">Academic Year:</span> 
            <span className="text-slate-800">{formData.academicYear}</span>
          </div>
        </div>
      </div>
    </div>
  );

  const renderCurrentStep = () => {
    switch (currentStep) {
      case 1: return renderPersonalInfoStep();
      case 2: return renderGuardianStep();
      case 3: return renderAcademicStep();
      case 4: return renderReviewStep();
      default: return null;
    }
  };

  return (
    <div className="fixed inset-0 bg-black/60 backdrop-blur-sm flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-2xl shadow-2xl shadow-emerald-900/20 max-w-4xl w-full max-h-[90vh] overflow-hidden border border-white/20">
        {/* Gradient Header */}
        <div className="relative overflow-hidden bg-gradient-to-br from-emerald-100 via-cyan-50 to-sky-100 px-6 py-5 border-b border-emerald-200/50">
          {/* Background Pattern */}
          <div className="absolute inset-0 bg-gradient-to-r from-emerald-200/20 via-transparent to-sky-200/20"></div>
          <div className="absolute top-0 right-0 w-24 h-24 bg-gradient-to-bl from-cyan-200/30 to-transparent rounded-full blur-xl"></div>
          <div className="absolute bottom-0 left-0 w-24 h-24 bg-gradient-to-tr from-emerald-200/30 to-transparent rounded-full blur-xl"></div>
          
          <div className="relative z-10">
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center space-x-3">
                <div className="w-12 h-12 bg-gradient-to-br from-emerald-500 to-cyan-600 rounded-xl flex items-center justify-center shadow-lg shadow-emerald-500/30">
                  <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 4v16m8-8H4" />
                  </svg>
                </div>
                <div>
                  <h2 className="text-xl font-bold bg-gradient-to-r from-emerald-800 via-cyan-700 to-sky-800 bg-clip-text text-transparent">
                    Add New Student
                  </h2>
                  <p className="text-sm text-slate-600">Complete all steps to enroll a new student</p>
                </div>
              </div>
              <button
                onClick={onClose}
                className="text-slate-500 hover:text-slate-700 bg-white/70 hover:bg-white/90 backdrop-blur-sm rounded-lg p-2 transition-all duration-200 shadow-sm border border-white/50"
              >
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>
            
            {/* Progress Steps - Blended with gradient */}
            <div className="mt-4">
              <div className="flex items-center justify-between">
                {steps.map((step, index) => (
                  <div key={step.number} className="flex items-center">
                    <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium transition-all duration-200 ${
                      currentStep >= step.number 
                        ? 'bg-gradient-to-r from-emerald-500 to-cyan-600 text-white shadow-lg shadow-emerald-500/30' 
                        : 'bg-white/60 backdrop-blur-sm text-slate-600 border border-white/70'
                    }`}>
                      {currentStep > step.number ? (
                        <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                        </svg>
                      ) : (
                        step.number
                      )}
                    </div>
                    <div className="ml-2">
                      <span className={`text-sm font-semibold transition-colors drop-shadow-sm ${
                        currentStep >= step.number ? 'text-emerald-800' : 'text-slate-600'
                      }`}>
                        {step.title}
                      </span>
                      <div className={`text-xs transition-colors drop-shadow-sm ${
                        currentStep >= step.number ? 'text-emerald-700' : 'text-slate-500'
                      }`}>
                        {step.description}
                      </div>
                    </div>
                    {index < steps.length - 1 && (
                      <div className={`w-12 h-0.5 mx-4 transition-colors ${
                        currentStep > step.number 
                          ? 'bg-gradient-to-r from-emerald-500 to-cyan-600' 
                          : 'bg-white/50 backdrop-blur-sm'
                      }`} />
                    )}
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>

        {/* Content - Fixed Height */}
        <div className="px-6 py-6 overflow-y-auto h-96">
          <div className="min-h-full">
            {renderCurrentStep()}
          </div>
        </div>

        {/* Footer */}
        <div className="px-6 py-4 bg-slate-50 border-t border-slate-200 flex items-center justify-between">
          <button
            onClick={handlePrevious}
            disabled={currentStep === 1}
            className="px-6 py-2.5 text-sm font-medium text-slate-700 bg-white border border-slate-300 rounded-lg hover:bg-slate-50 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 shadow-sm"
          >
            <div className="flex items-center space-x-2">
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 19l-7-7 7-7" />
              </svg>
              <span>Previous</span>
            </div>
          </button>
          
          <div className="flex items-center space-x-2">
            <span className="text-sm text-slate-500 bg-white px-3 py-1 rounded-md border border-slate-200">
              Step {currentStep} of {steps.length}
            </span>
          </div>
          
          {currentStep === steps.length ? (
            <button
              onClick={handleSubmit}
              disabled={isSubmitting}
              className="px-6 py-2.5 text-sm font-medium text-white bg-gradient-to-r from-emerald-600 to-cyan-600 hover:from-emerald-700 hover:to-cyan-700 border border-transparent rounded-lg disabled:opacity-50 transition-all duration-200 shadow-lg shadow-emerald-500/30"
            >
              <div className="flex items-center space-x-2">
                {isSubmitting ? (
                  <>
                    <svg className="w-4 h-4 animate-spin" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                      <path className="opacity-75" fill="currentColor" d="m4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    <span>Adding Student...</span>
                  </>
                ) : (
                  <>
                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 4v16m8-8H4" />
                    </svg>
                    <span>Add Student</span>
                  </>
                )}
              </div>
            </button>
          ) : (
            <button
              onClick={handleNext}
              className="px-6 py-2.5 text-sm font-medium text-white bg-gradient-to-r from-emerald-600 to-cyan-600 hover:from-emerald-700 hover:to-cyan-700 border border-transparent rounded-lg transition-all duration-200 shadow-lg shadow-emerald-500/30"
            >
              <div className="flex items-center space-x-2">
                <span>Next</span>
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 5l7 7-7 7" />
                </svg>
              </div>
            </button>
          )}
        </div>
      </div>
    </div>
  );
};

export default AddStudentWizard;
